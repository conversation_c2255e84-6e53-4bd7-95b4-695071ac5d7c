# 客戶端發貨功能移除方案

**問題描述**: 客戶端（mall-client）中存在不應該有的發貨按鈕和發貨功能，這些功能應該只在管理後台存在。

**問題分析**: 
1. 普通用戶（買家）不應該有發貨權限
2. 發貨功能應該只在管理後台（mall-admin）中存在
3. 客戶端的訂單頁面應該只能查看訂單狀態，不能進行發貨操作

## 解決方案

### 1. 移除普通用戶訂單頁面的發貨功能

**文件**: `mall-client/src/app/orders/page.tsx`

**移除的內容**:
- 發貨模態框相關狀態
- 管理員權限檢查
- 發貨按鈕和相關邏輯
- ShippingModal 組件引用

**保留的功能**:
- 訂單列表查看
- 訂單狀態顯示
- 訂單詳情查看按鈕

### 2. 刪除不應該存在的組件

**刪除文件**: `mall-client/src/components/Order/ShippingModal.tsx`

**原因**: 普通用戶不應該有發貨功能，此組件應該只在管理後台存在

### 3. 創建管理員專用發貨組件

**新建文件**: `mall-client/src/components/Admin/ShippingModal.tsx`

**用途**: 
- 僅供管理員頁面使用
- 明確標識為管理員功能
- 與普通用戶功能分離

### 4. 更新管理員訂單頁面

**文件**: `mall-client/src/app/admin/orders/page.tsx`

**更新內容**:
- 使用新的 AdminShippingModal 組件
- 保持發貨功能完整性

## 修改詳情

### 修改前的問題

```typescript
// 在普通用戶訂單頁面中存在：
const isAdmin = true; // 模擬管理員權限
const canShipOrder = (status: OrderStatus) => { ... };
const openShippingModal = (orderId: number, orderNumber: string) => { ... };

// 發貨按鈕
{isAdmin && canShipOrder(order.status) && (
  <button onClick={() => openShippingModal(order.id!, order.orderNumber)}>
    發貨
  </button>
)}

// 發貨模態框
<ShippingModal ... />
```

### 修改後的結果

```typescript
// 普通用戶訂單頁面只保留：
<Link href={`/orders/${order.id}`}>
  <EyeIcon className="w-4 h-4" />
  <span>查看詳情</span>
</Link>
```

## 權限分離

### 普通用戶（買家）權限
- ✅ 查看自己的訂單
- ✅ 查看訂單詳情
- ✅ 查看訂單狀態
- ❌ 發貨操作
- ❌ 修改訂單狀態

### 管理員權限
- ✅ 查看所有訂單
- ✅ 發貨操作
- ✅ 修改訂單狀態
- ✅ 訂單管理

## 文件結構

```
mall-client/
├── src/
│   ├── app/
│   │   ├── orders/page.tsx          # 普通用戶訂單頁面（無發貨功能）
│   │   └── admin/
│   │       └── orders/page.tsx      # 管理員訂單頁面（有發貨功能）
│   └── components/
│       ├── Admin/
│       │   └── ShippingModal.tsx    # 管理員專用發貨模態框
│       └── Order/
│           └── [其他訂單組件]       # 普通用戶訂單組件
```

## 安全考慮

1. **前端權限控制**: 移除了普通用戶界面中的發貨功能
2. **後端權限驗證**: 後端 API 仍需要 `@PreAuthorize("hasRole('ADMIN')")` 註解
3. **路由保護**: 管理員頁面應該有適當的路由保護
4. **用戶體驗**: 普通用戶界面更加簡潔，避免混淆

## 測試建議

1. **普通用戶測試**:
   - 訪問 `/orders` 頁面
   - 確認沒有發貨按鈕
   - 確認只能查看訂單詳情

2. **管理員測試**:
   - 訪問 `/admin/orders` 頁面
   - 確認發貨功能正常
   - 測試發貨流程

3. **權限測試**:
   - 確認普通用戶無法訪問管理員頁面
   - 確認後端 API 權限驗證正常

## 後續改進建議

1. **路由保護**: 為管理員頁面添加身份驗證中間件
2. **角色管理**: 完善用戶角色和權限系統
3. **UI 優化**: 為管理員和普通用戶提供不同的界面主題
4. **審計日誌**: 記錄管理員操作日誌
