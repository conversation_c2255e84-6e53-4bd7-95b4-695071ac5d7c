'use client';

import React from 'react';
import { useCartStore } from '../../store/cartStore';

/**
 * 購物車測試頁面
 * 用於測試購物車功能是否正常工作
 */
export default function TestCartPage() {
  const { items, totalItems, totalPrice, addItem, clearCart, isHydrated } = useCartStore();

  // 測試商品數據
  const testProducts = [
    {
      id: 1,
      name: '測試商品 1',
      price: 99.99,
      imageUrl: '/test1.jpg',
      stock: 10
    },
    {
      id: 2,
      name: '測試商品 2',
      price: 199.99,
      imageUrl: '/test2.jpg',
      stock: 5
    },
    {
      id: 3,
      name: '測試商品 3',
      price: 299.99,
      imageUrl: '/test3.jpg',
      stock: 3
    }
  ];

  const handleAddTestProduct = (product: typeof testProducts[0]) => {
    addItem(product);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">購物車功能測試</h1>
      
      {/* 狀態信息 */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-xl font-semibold mb-2">當前狀態</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="font-medium">水合狀態:</span>
            <span className={`ml-2 ${isHydrated ? 'text-green-600' : 'text-red-600'}`}>
              {isHydrated ? '✅ 已完成' : '❌ 未完成'}
            </span>
          </div>
          <div>
            <span className="font-medium">商品種類:</span>
            <span className="ml-2">{items.length}</span>
          </div>
          <div>
            <span className="font-medium">總件數:</span>
            <span className="ml-2">{totalItems}</span>
          </div>
          <div>
            <span className="font-medium">總價格:</span>
            <span className="ml-2">¥{totalPrice.toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* 測試商品 */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">測試商品</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {testProducts.map((product) => (
            <div key={product.id} className="border rounded-lg p-4">
              <h3 className="font-semibold">{product.name}</h3>
              <p className="text-gray-600">價格: ¥{product.price}</p>
              <p className="text-gray-600">庫存: {product.stock}</p>
              <button
                onClick={() => handleAddTestProduct(product)}
                disabled={!isHydrated}
                className={`mt-2 w-full py-2 px-4 rounded ${
                  isHydrated
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                {isHydrated ? '加入購物車' : '載入中...'}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* 購物車內容 */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">購物車內容</h2>
        {items.length === 0 ? (
          <p className="text-gray-600">購物車為空</p>
        ) : (
          <div className="space-y-2">
            {items.map((item) => (
              <div key={item.id} className="flex justify-between items-center bg-white border rounded p-3">
                <div>
                  <span className="font-medium">{item.name}</span>
                  <span className="text-gray-600 ml-2">x{item.quantity}</span>
                </div>
                <div className="text-right">
                  <div>¥{item.price.toFixed(2)}</div>
                  <div className="text-sm text-gray-600">
                    小計: ¥{(item.price * item.quantity).toFixed(2)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 操作按鈕 */}
      <div className="flex space-x-4">
        <button
          onClick={clearCart}
          disabled={!isHydrated || items.length === 0}
          className={`py-2 px-4 rounded ${
            isHydrated && items.length > 0
              ? 'bg-red-600 text-white hover:bg-red-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          清空購物車
        </button>
        
        <a
          href="/cart"
          className={`py-2 px-4 rounded ${
            isHydrated && items.length > 0
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          前往購物車頁面
        </a>
        
        <a
          href="/checkout"
          className={`py-2 px-4 rounded ${
            isHydrated && items.length > 0
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          前往結算頁面
        </a>
      </div>

      {/* 本地存儲信息 */}
      <div className="mt-6 bg-gray-100 p-4 rounded-lg">
        <h2 className="text-xl font-semibold mb-2">本地存儲信息</h2>
        <div className="text-sm">
          <div className="mb-2">
            <span className="font-medium">存儲鍵:</span>
            <span className="ml-2">cart-storage</span>
          </div>
          <div>
            <span className="font-medium">存儲內容:</span>
            <pre className="mt-2 p-2 bg-white border rounded text-xs overflow-auto">
              {typeof window !== 'undefined' 
                ? localStorage.getItem('cart-storage') || '無數據'
                : '服務端渲染中...'}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
