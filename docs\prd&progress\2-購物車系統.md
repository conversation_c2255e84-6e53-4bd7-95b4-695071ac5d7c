# 2-購物車系統

**上級方案**: [0-總](0-總.md)  
**前置依賴**: [1-商品展示模塊](1-商品展示模塊.md)

## 方案概述

開發完整的購物車系統，包括狀態管理、購物車頁面、添加到購物車功能、以及購物車數量顯示，為用戶提供流暢的購物體驗。

## 技術方案

### 狀態管理
- **Zustand**: 輕量級狀態管理庫
- **本地存儲**: 使用 persist 中間件實現數據持久化
- **TypeScript**: 完整的類型安全

### 用戶體驗
- **Toast 通知**: react-hot-toast 提供友好的操作反饋
- **響應式設計**: 桌面端和移動端適配
- **實時更新**: 購物車數量實時同步

## 實施計劃

### 階段1：狀態管理開發
- [x] 設計CartItem接口
- [x] 創建cartStore.ts
- [x] 實現基本CRUD操作
- [x] 添加本地存儲持久化

### 階段2：核心組件開發
- [x] CartItem組件 - 購物車商品項
- [x] CartSummary組件 - 購物車總計
- [x] AddToCartButton組件 - 添加按鈕

### 階段3：頁面集成
- [x] 購物車頁面 (/cart)
- [x] Header購物車徽章
- [x] 商品頁面集成

### 階段4：業務邏輯完善
- [x] 庫存檢查機制
- [x] 運費計算邏輯
- [x] 錯誤處理和提示

## 實現詳情

### 1. 購物車狀態管理 (cartStore.ts)
```typescript
interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  addItem: (product) => void;
  removeItem: (id) => void;
  updateQuantity: (id, quantity) => void;
  clearCart: () => void;
  getItemQuantity: (id) => number;
}
```

**核心功能：**
- ✅ 添加商品到購物車
- ✅ 更新商品數量（不超過庫存）
- ✅ 移除商品
- ✅ 清空購物車
- ✅ 自動計算總數量和總價格
- ✅ 本地存儲持久化

### 2. 購物車組件

#### CartItem 組件
- ✅ 商品信息展示（圖片、名稱、價格）
- ✅ 數量控制（+/-按鈕）
- ✅ 庫存限制檢查
- ✅ 刪除商品功能
- ✅ 小計計算

#### CartSummary 組件
- ✅ 訂單摘要（商品數量、小計、運費）
- ✅ 免運費邏輯（滿99元免運費）
- ✅ 總計計算
- ✅ 結算按鈕
- ✅ 安全提示

#### AddToCartButton 組件
- ✅ 多種尺寸和樣式變體
- ✅ 加載狀態動畫
- ✅ 成功狀態反饋
- ✅ 庫存檢查
- ✅ Toast 通知

### 3. 購物車頁面 (/cart)
- ✅ 空購物車狀態處理
- ✅ 商品列表展示
- ✅ 響應式佈局（桌面端和移動端）
- ✅ 繼續購物引導
- ✅ 移動端固定底部結算按鈕

### 4. Header 集成
- ✅ 購物車圖標
- ✅ 數量徽章（桌面端和移動端）
- ✅ 超過99件顯示"99+"
- ✅ 實時數量更新

### 5. 商品頁面集成
- ✅ 商品詳情頁添加到購物車按鈕
- ✅ 商品卡片添加到購物車按鈕
- ✅ 防止事件冒泡處理

## 文件結構

```
src/
├── store/
│   └── cartStore.ts                 # 購物車狀態管理
├── components/
│   └── Cart/
│       ├── CartItem.tsx            # 購物車商品項
│       ├── CartSummary.tsx         # 購物車總計
│       └── AddToCartButton.tsx     # 添加到購物車按鈕
├── app/
│   ├── cart/
│   │   └── page.tsx               # 購物車頁面
│   └── layout.tsx                 # Toast 提供者
└── components/Layout/
    └── Header.tsx                 # 購物車數量徽章
```

## 業務邏輯

### 運費計算
- 滿99元免運費
- 不滿99元收取10元運費
- 顯示距離免運費的差額

### 庫存管理
- 添加商品時檢查庫存
- 數量調整不能超過庫存
- 庫存不足時顯示提示

### 數據持久化
- 使用 localStorage 存儲購物車數據
- 頁面刷新後數據保持
- 跨會話數據保存

## 測試結果

### 功能測試
- ✅ 添加商品到購物車
- ✅ 修改商品數量
- ✅ 刪除商品
- ✅ 清空購物車
- ✅ 購物車數量顯示
- ✅ 總價計算
- ✅ 運費計算
- ✅ 本地存儲持久化

### 用戶體驗測試
- ✅ Toast 通知正常
- ✅ 響應式佈局適配
- ✅ 加載狀態動畫
- ✅ 庫存限制提示
- ✅ 空購物車狀態

### 瀏覽器兼容性
- ✅ Chrome (測試通過)
- ✅ 移動端響應式 (測試通過)

## 技術特性

### 1. 類型安全
- 完整的 TypeScript 類型定義
- 接口約束確保數據一致性

### 2. 性能優化
- Zustand 輕量級狀態管理
- 組件級別的優化渲染
- 本地存儲減少網絡請求

### 3. 用戶體驗
- 友好的 Toast 通知
- 加載狀態和成功反饋
- 庫存限制提示
- 響應式設計

### 4. 錯誤處理
- 庫存不足提示
- 操作確認對話框
- 優雅的錯誤降級

## 後續改進建議

### 1. 功能增強
- 商品規格選擇（顏色、尺寸）
- 購物車商品收藏
- 批量操作（全選、批量刪除）
- 購物車分享功能

### 2. 性能優化
- 虛擬滾動（大量商品時）
- 圖片懶加載優化
- 狀態更新防抖

### 3. 用戶體驗
- 拖拽排序
- 商品推薦
- 最近瀏覽記錄
- 購物車提醒

### 4. 數據同步
- 與後端購物車API同步
- 多設備購物車同步
- 離線狀態處理

## 交付物

### 代碼文件
- cartStore.ts - 購物車狀態管理
- Cart組件集合 - 購物車相關組件
- 購物車頁面 - 完整頁面實現
- 集成代碼 - Header和商品頁面集成

### 文檔
- 狀態管理文檔
- 組件使用指南
- 業務邏輯說明
- 測試報告

## 驗收標準

- [x] 購物車基本功能完整
- [x] 狀態管理穩定可靠
- [x] 用戶體驗流暢友好
- [x] 響應式設計適配
- [x] 錯誤處理完善
- [x] 代碼質量達標

---

**完成確認**: ✅ 2025-01-27  
**下一步**: [3-訂單系統](3-訂單系統.md)
